import { motion } from "framer-motion";
import { useState, useEffect } from "react";
import parse from "html-react-parser";

const MizuTemplatePreview = ({
  selectedTemplate,
  companyData,
  onBack,
  onProceed,
  variants
}) => {
  const [renderedHtml, setRenderedHtml] = useState("");
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState("");

  // Static data for preview
  const staticInvoiceData = {
    invoice: {
      number: "INV-2024-001",
      date: "2024-01-15",
      due_date: "2024-02-14",
      po_number: "PO-12345",
      subtotal: 4905.00,
      tax_rate: 8.5,
      tax_amount: 416.93,
      discount_amount: 0,
      total: 5321.93,
      items: [
        {
          description: "Web Development Services",
          quantity: 40,
          rate: 50.00,
          amount: 2000.00
        },
        {
          description: "UI/UX Design Consultation",
          quantity: 10,
          rate: 75.00,
          amount: 750.00
        },
        {
          description: "Database Setup & Configuration",
          quantity: 8,
          rate: 65.00,
          amount: 520.00
        },
        {
          description: "API Integration Services",
          quantity: 12,
          rate: 80.00,
          amount: 960.00
        },
        {
          description: "Testing & Quality Assurance",
          quantity: 15,
          rate: 45.00,
          amount: 675.00
        }
      ]
    },
    client: {
      name: "Acme Corporation",
      address: "456 Client Street, Business City, BC 12345",
      email: "<EMAIL>",
      phone: "+****************"
    },
    company: {
      ...companyData,
      logo: companyData.logo ? URL.createObjectURL(companyData.logo) : null
    }
  };

  useEffect(() => {
    if (selectedTemplate && companyData) {
      renderTemplate();
    }
  }, [selectedTemplate, companyData]);

  // Simple template renderer using regex replacement
  const renderTemplateWithSimpleReplacement = (template, data) => {
    let rendered = template;

    console.log('Starting template rendering with data:', data);
    console.log('Template length:', template.length);

    // Flatten the data object for easier access
    const flattenData = (obj, prefix = '') => {
      const flattened = {};

      for (const [key, value] of Object.entries(obj)) {
        const newKey = prefix ? `${prefix}.${key}` : key;

        if (value && typeof value === 'object' && !Array.isArray(value)) {
          Object.assign(flattened, flattenData(value, newKey));
        } else {
          flattened[newKey] = value;

          // Add formatted versions for numbers
          if (typeof value === 'number') {
            flattened[`${newKey}_formatted`] = value.toFixed(2);
          }
        }
      }

      return flattened;
    };

    const flatData = flattenData(data);
    console.log('Flattened data:', flatData);

    // Handle {% for item in invoice.items %} loops FIRST (before variable replacement)
    // This is crucial because we need to process the loop before replacing variables
    rendered = rendered.replace(/{% for item in invoice\.items %}([\s\S]*?){% endfor %}/g, (match, itemTemplate) => {
      const items = data.invoice?.items || [];

      console.log('Processing line items:', items);
      console.log('Item template:', itemTemplate);

      return items.map(item => {
        let itemHtml = itemTemplate;

        // Replace {{ item.property }} with actual item values
        itemHtml = itemHtml.replace(/{{\s*item\.([^}|]+?)\s*}}/g, (itemMatch, property) => {
          const value = item[property] ?? '';
          console.log(`Replacing {{ item.${property} }} with:`, value);
          return value;
        });

        // Replace {{ item.property|floatformat:2 }} with formatted values
        itemHtml = itemHtml.replace(/{{\s*item\.([^}|]+?)\|floatformat:2\s*}}/g, (itemMatch, property) => {
          const value = item[property];
          const formatted = value !== undefined && value !== null ? parseFloat(value).toFixed(2) : '0.00';
          console.log(`Replacing {{ item.${property}|floatformat:2 }} with:`, formatted);
          return formatted;
        });

        console.log('Final item HTML:', itemHtml);
        return itemHtml;
      }).join('');
    });

    // Now replace simple variables: {{ key }} or {{ nested.key }}
    // But exclude item.* variables as they should have been processed in the loop
    rendered = rendered.replace(/{{\s*([^}|]+?)\s*}}/g, (match, key) => {
      const cleanKey = key.trim();
      // Skip item.* variables as they should have been processed in the for loop
      if (cleanKey.startsWith('item.')) {
        return match; // Return original if it's an item variable that wasn't processed
      }
      return flatData[cleanKey] ?? '';
    });

    // Replace variables with floatformat filter: {{ key|floatformat:2 }}
    rendered = rendered.replace(/{{\s*([^}|]+?)\|floatformat:2\s*}}/g, (match, key) => {
      const cleanKey = key.trim();
      // Skip item.* variables
      if (cleanKey.startsWith('item.')) {
        return match;
      }
      const value = flatData[cleanKey];
      return value !== undefined && value !== null ? parseFloat(value).toFixed(2) : '0.00';
    });

    // Replace variables with default filter: {{ key|default:"text" }}
    rendered = rendered.replace(/{{\s*([^}|]+?)\|default:"([^"]+)"\s*}}/g, (match, key, defaultValue) => {
      const cleanKey = key.trim();
      // Skip item.* variables
      if (cleanKey.startsWith('item.')) {
        return match;
      }
      const value = flatData[cleanKey];
      return value !== undefined && value !== null && value !== '' ? value : defaultValue;
    });

    // Handle {% if condition %} blocks - simple approach
    rendered = rendered.replace(/{% if ([^%]+?) %}([\s\S]*?){% endif %}/g, (match, condition, content) => {
      const cleanCondition = condition.trim();

      // Handle "not" conditions
      if (cleanCondition.startsWith('not ')) {
        const key = cleanCondition.substring(4).trim();
        const value = flatData[key];
        const isTrue = value && value !== '' && value !== 0;
        return !isTrue ? content : '';
      } else {
        const value = flatData[cleanCondition];
        const isTrue = value && value !== '' && value !== 0;
        return isTrue ? content : '';
      }
    });

    // Clean up any remaining template tags that weren't processed
    // Be more specific about cleanup to avoid removing valid content
    console.log('Before cleanup, rendered length:', rendered.length);
    console.log('Checking for remaining template tags...');

    const beforeCleanup = rendered;
    rendered = rendered.replace(/{%\s*endif\s*%}/gi, '');
    rendered = rendered.replace(/{%\s*endfor\s*%}/gi, '');
    rendered = rendered.replace(/{%\s*else\s*%}/gi, '');
    rendered = rendered.replace(/{%[^}]*%}/g, ''); // Remove any other unprocessed template tags

    // Only clean up specific leftover template syntax, not all {{ }} patterns
    // Remove empty or unmatched variables but preserve content
    rendered = rendered.replace(/\{\{\s*\|\s*[^}]*\s*\}\}/g, ''); // Remove filter-only patterns like {{ |default:"text" }}
    rendered = rendered.replace(/\{\{\s*\}\}/g, ''); // Remove completely empty {{ }}

    if (beforeCleanup !== rendered) {
      console.log('Template tags were cleaned up');
    }

    console.log('After cleanup, rendered length:', rendered.length);

    return rendered;
  };

  // Function to extract body content and styles from full HTML document
  // This prevents React from trying to mount multiple <html> and <body> elements
  // which causes the "mounting a new html/body component" error
  const extractBodyContentWithStyles = (htmlContent) => {
    try {
      // Extract styles from head section
      const styleMatch = htmlContent.match(/<style[^>]*>([\s\S]*?)<\/style>/i);
      const styles = styleMatch ? styleMatch[1] : '';

      // Extract content from body section
      const bodyMatch = htmlContent.match(/<body[^>]*>([\s\S]*?)<\/body>/i);
      const bodyContent = bodyMatch ? bodyMatch[1] : htmlContent;

      // Combine styles and body content
      const processedHtml = styles
        ? `<style>${styles}</style>${bodyContent}`
        : bodyContent;

      return processedHtml;
    } catch (error) {
      console.error('Error extracting body content:', error);
      // Fallback: return original content if extraction fails
      return htmlContent;
    }
  };

  // Template renderer function using simple replacement
  const renderTemplate = async () => {
    try {
      setLoading(true);
      setError("");

      // Use simple template replacement instead of Mustache
      const rendered = renderTemplateWithSimpleReplacement(
        selectedTemplate.html_content,
        staticInvoiceData
      );

      // Extract only body content and styles to avoid html/body tag conflicts
      const processedHtml = extractBodyContentWithStyles(rendered);

      console.log("Template rendering completed");
      console.log("Static data:", staticInvoiceData);
      console.log("Rendered HTML length:", processedHtml.length);

      setRenderedHtml(processedHtml);
    } catch (err) {
      console.error("Template rendering error:", err);
      setError("Failed to render template: " + err.message);
    } finally {
      setLoading(false);
    }
  };

  const handleProceed = () => {
    // Pass the rendered data to the next step
    onProceed({
      template: selectedTemplate,
      companyData: companyData,
      staticData: staticInvoiceData,
      renderedHtml: renderedHtml
    });
  };

  if (loading) {
    return (
      <motion.div
        className="flex flex-col items-center justify-center py-12"
        variants={variants}
      >
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
        <p className="mt-4 text-gray-600">Rendering template...</p>
      </motion.div>
    );
  }

  if (error) {
    return (
      <motion.div
        className="flex flex-col items-center justify-center py-12"
        variants={variants}
      >
        <div className="text-red-600 text-center">
          <p className="text-lg font-medium mb-2">Template Rendering Error</p>
          <p className="text-sm mb-4">{error}</p>
          <button
            onClick={renderTemplate}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors mr-2"
          >
            Try Again
          </button>
          <button
            onClick={onBack}
            className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
          >
            Go Back
          </button>
        </div>
      </motion.div>
    );
  }

  return (
    <motion.div
      className="flex flex-col gap-6"
      variants={variants}
    >
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-xl font-medium text-gray-700">
            Template Preview
          </h2>
          <p className="text-gray-600 mt-1">
            Preview of {selectedTemplate.name} template with your company data
          </p>
        </div>
        <div className="flex gap-2">
          <button
            onClick={onBack}
            className="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors"
          >
            ← Back
          </button>
          <button
            onClick={handleProceed}
            className="px-6 py-2 bg-blue-600 text-white font-medium rounded-lg hover:bg-blue-700 transition-colors"
          >
            Continue
          </button>
        </div>
      </div>

      {/* Template Info */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <div className="flex items-center gap-3">
          <div className="w-10 h-10 bg-blue-600 rounded-lg flex items-center justify-center">
            <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
          </div>
          <div>
            <h3 className="font-medium text-blue-900">{selectedTemplate.name}</h3>
            <p className="text-sm text-blue-700">{selectedTemplate.description}</p>
          </div>
        </div>
      </div>

      {/* Preview Container */}
      <div className="border border-gray-200 rounded-lg overflow-hidden bg-white shadow-sm">
        <div className="bg-gray-50 px-4 py-2 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium text-gray-700">Invoice Preview</span>
            <div className="flex items-center gap-2 text-xs text-gray-500">
              <span>Sample data is used for demonstration</span>
            </div>
          </div>
        </div>

        <div className="p-4 overflow-auto" style={{ maxHeight: '800px', minHeight: '600px' }}>
          {renderedHtml && (
            <div className="invoice-preview" style={{
              fontSize: '14px',
              lineHeight: '1.5',
              transform: 'scale(0.9)',
              transformOrigin: 'top center',
              width: '100%',
              maxWidth: '800px',
              margin: '0 auto',
              backgroundColor: 'white',
              boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
              borderRadius: '4px'
            }}>
              {parse(renderedHtml)}
            </div>
          )}
        </div>
      </div>



      {/* Note */}
      <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
        <div className="flex items-start gap-3">
          <svg className="w-5 h-5 text-yellow-600 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          <div>
            <p className="text-sm text-yellow-800">
              <strong>Note:</strong> This is a preview with sample data. In the actual invoice generation,
              you'll be able to customize all the invoice details, line items, and client information.
            </p>
          </div>
        </div>
      </div>
    </motion.div>
  );
};

export default MizuTemplatePreview;
